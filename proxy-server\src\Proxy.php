<?php

namespace App;

use Proxy\Http\Request;
use Proxy\Proxy;
use Proxy\Adapter\Guzzle\GuzzleAdapter;
use Proxy\Filter\RemoveEncodingFilter;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class ProxyHandler {
    private $config;
    private $translations;
    private $proxy;
    private $request;
    
    public function __construct($config, $translations) {
        $this->config = $config;
        $this->translations = $translations;
        $this->request = Request::createFromGlobals();
        
        // Initialize proxy
        $this->proxy = new Proxy(new GuzzleAdapter());
        
        // Apply filters
        $this->proxy->filter(new RemoveEncodingFilter());
        
        // Add custom filters
        $this->addCustomFilters();
    }
    
    public function handle() {
        try {
            // Security checks
            $this->performSecurityChecks();
            
            // Get target URL
            $targetUrl = $this->getTargetUrl();
            if (!$targetUrl) {
                return $this->renderHomePage();
            }
            
            // Validate and filter URL
            if (!$this->validateUrl($targetUrl)) {
                throw new \Exception($this->translations['error_invalid_url']);
            }
            
            // Check site filtering
            $this->checkSiteFiltering($targetUrl);
            
            // Apply rate limiting
            $this->checkRateLimit();
            
            // Forward the request
            $response = $this->forwardRequest($targetUrl);
            
            // Process response
            return $this->processResponse($response, $targetUrl);
            
        } catch (\Exception $e) {
            return $this->handleError($e);
        }
    }
    
    private function renderHomePage() {
        $template = $this->getTemplateEngine();
        
        return $template->render('proxy.php', [
            'title' => $this->translations['site_title'],
            'description' => $this->translations['site_description'],
            'site_title' => $this->config['server']['name'],
            'server_name' => $this->config['server']['name'],
            'current_url' => '',
            'current_language' => $this->getCurrentLanguage(),
            'languages' => $this->config['languages'],
            'lang' => $this->getCurrentLanguageCode(),
            'header_ads' => $this->config['ads']['header'],
            'footer_ads' => $this->config['ads']['footer'],
            'sidebar_ads' => $this->config['ads']['sidebar'],
            'show_quick_settings' => true,
            'settings' => $this->getUserSettings(),
            // Translations
            ...$this->translations
        ]);
    }
    
    private function getTargetUrl() {
        $url = $this->request->getQuery()->get('url');
        if ($url) {
            return $this->sanitizeUrl($url);
        }
        return null;
    }
    
    private function sanitizeUrl($url) {
        // Remove dangerous characters and protocols
        $url = trim($url);
        $url = preg_replace('/[\x00-\x1F\x7F]/', '', $url);
        
        // Ensure URL has a protocol
        if (!preg_match('~^https?://~i', $url)) {
            $url = 'https://' . $url;
        }
        
        return $url;
    }
    
    private function validateUrl($url) {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check for blocked protocols
        $parsed = parse_url($url);
        if (!in_array($parsed['scheme'], ['http', 'https'])) {
            return false;
        }
        
        return true;
    }
    
    private function performSecurityChecks() {
        // Check HTTPS requirement
        if ($this->config['security']['require_https'] && !$this->isHttps()) {
            throw new \Exception($this->translations['security_https_required']);
        }
        
        // Check user agent
        $userAgent = $this->request->getHeaderLine('User-Agent');
        foreach ($this->config['security']['block_user_agents'] as $blocked) {
            if (stripos($userAgent, $blocked) !== false) {
                throw new \Exception($this->translations['security_blocked_agent']);
            }
        }
        
        // Check IP blocking
        $clientIp = $this->getClientIp();
        if (in_array($clientIp, $this->config['security']['blocked_ips'])) {
            throw new \Exception($this->translations['security_blocked_ip']);
        }
        
        // Check referer
        $referer = $this->request->getHeaderLine('Referer');
        if (!empty($this->config['security']['allowed_referers']) && $referer) {
            $allowed = false;
            foreach ($this->config['security']['allowed_referers'] as $allowedReferer) {
                if (strpos($referer, $allowedReferer) !== false) {
                    $allowed = true;
                    break;
                }
            }
            if (!$allowed) {
                throw new \Exception($this->translations['security_invalid_referer']);
            }
        }
    }
    
    private function checkSiteFiltering($url) {
        $host = parse_url($url, PHP_URL_HOST);
        
        // Check allowed sites
        if (!empty($this->config['filtering']['allowed_sites'])) {
            $allowed = false;
            foreach ($this->config['filtering']['allowed_sites'] as $allowedSite) {
                if (strpos($host, $allowedSite) !== false) {
                    $allowed = true;
                    break;
                }
            }
            if (!$allowed) {
                throw new \Exception($this->translations['error_site_blocked']);
            }
        }
        
        // Check blocked sites
        foreach ($this->config['filtering']['blocked_sites'] as $blockedSite) {
            if (strpos($host, $blockedSite) !== false) {
                throw new \Exception($this->translations['error_site_blocked']);
            }
        }
        
        // Check blocked keywords
        foreach ($this->config['filtering']['blocked_keywords'] as $keyword) {
            if (stripos($url, $keyword) !== false) {
                throw new \Exception($this->translations['error_malicious_content']);
            }
        }
        
        // Check blocked extensions
        $path = parse_url($url, PHP_URL_PATH);
        if ($path) {
            foreach ($this->config['filtering']['blocked_extensions'] as $ext) {
                if (strtolower(substr($path, -strlen($ext))) === strtolower($ext)) {
                    throw new \Exception($this->translations['file_type_blocked']);
                }
            }
        }
    }
    
    private function checkRateLimit() {
        $maxRequests = $this->config['security']['max_requests_per_minute'];
        if ($maxRequests > 0) {
            // Simple rate limiting using session
            session_start();
            $now = time();
            $windowStart = $now - 60;
            
            if (!isset($_SESSION['proxy_requests'])) {
                $_SESSION['proxy_requests'] = [];
            }
            
            // Clean old requests
            $_SESSION['proxy_requests'] = array_filter($_SESSION['proxy_requests'], function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
            
            // Check limit
            if (count($_SESSION['proxy_requests']) >= $maxRequests) {
                throw new \Exception($this->translations['error_rate_limited']);
            }
            
            // Add current request
            $_SESSION['proxy_requests'][] = $now;
        }
    }
    
    private function forwardRequest($targetUrl) {
        // Create a new request to the target
        $psrRequest = $this->createPsrRequest($targetUrl);
        
        // Set timeout
        $this->proxy->getClient()->setOption('timeout', $this->config['defaults']['timeout']);
        
        // Forward the request
        return $this->proxy->forward($psrRequest);
    }
    
    private function createPsrRequest($targetUrl) {
        $method = $this->request->getMethod();
        $headers = $this->getFilteredHeaders();
        $body = $this->request->getBody();
        
        // Create PSR-7 request
        $request = new \GuzzleHttp\Psr7\Request($method, $targetUrl, $headers, $body);
        
        return $request;
    }
    
    private function getFilteredHeaders() {
        $headers = [];
        $blockedHeaders = ['host', 'cookie', 'authorization'];
        
        foreach ($this->request->getHeaders() as $name => $values) {
            if (!in_array(strtolower($name), $blockedHeaders)) {
                $headers[$name] = $values;
            }
        }
        
        // Set custom user agent
        $headers['User-Agent'] = [$this->config['defaults']['user_agent']];
        
        return $headers;
    }
    
    private function processResponse(ResponseInterface $response, $targetUrl) {
        // Check content type
        $contentType = $response->getHeaderLine('Content-Type');
        
        // Handle different content types
        if (strpos($contentType, 'text/html') !== false) {
            return $this->processHtmlResponse($response, $targetUrl);
        } elseif (strpos($contentType, 'application/json') !== false) {
            return $this->processJsonResponse($response);
        } else {
            return $this->processBinaryResponse($response);
        }
    }
    
    private function processHtmlResponse(ResponseInterface $response, $targetUrl) {
        $body = (string) $response->getBody();
        
        // Inject ads
        if (!empty($this->config['ads']['inject_before_body'])) {
            $body = str_replace('<body', $this->config['ads']['inject_before_body'] . '<body', $body);
        }
        
        if (!empty($this->config['ads']['inject_after_body'])) {
            $body = str_replace('</body>', $this->config['ads']['inject_after_body'] . '</body>', $body);
        }
        
        // Rewrite URLs to proxy them
        $body = $this->rewriteUrls($body, $targetUrl);
        
        // Remove scripts if configured
        if ($this->config['proxy']['remove_scripts']) {
            $body = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $body);
        }
        
        // Create new response
        $newResponse = $response->withBody(\GuzzleHttp\Psr7\Utils::streamFor($body));
        
        return $newResponse;
    }
    
    private function rewriteUrls($html, $baseUrl) {
        // Rewrite URLs to go through the proxy
        $pattern = '/(href|src|action)=["\'](https?:\/\/[^"\']+)["\']/i';
        
        return preg_replace_callback($pattern, function($matches) use ($baseUrl) {
            $url = $matches[2];
            $proxyUrl = $this->config['server']['base_url'] . '?url=' . urlencode($url);
            return $matches[1] . '="' . $proxyUrl . '"';
        }, $html);
    }
    
    private function processJsonResponse(ResponseInterface $response) {
        // JSON responses are passed through as-is
        return $response;
    }
    
    private function processBinaryResponse(ResponseInterface $response) {
        // Check file size
        $contentLength = $response->getHeaderLine('Content-Length');
        if ($contentLength && $contentLength > $this->config['defaults']['max_file_size']) {
            throw new \Exception($this->translations['error_file_too_large']);
        }
        
        return $response;
    }
    
    private function addCustomFilters() {
        // Add custom filters here if needed
    }
    
    private function getCurrentLanguage() {
        return $this->config['languages'][$this->getCurrentLanguageCode()] ?? 'English';
    }
    
    private function getCurrentLanguageCode() {
        return $_GET['lang'] ?? $_COOKIE['proxy_lang'] ?? $this->config['defaults']['language'];
    }
    
    private function getUserSettings() {
        return [
            'javascript' => $this->config['proxy']['enable_javascript'],
            'cookies' => $this->config['proxy']['enable_cookies'],
            'images' => true // Could be made configurable
        ];
    }
    
    private function getTemplateEngine() {
        $loader = new \Twig\Loader\FilesystemLoader(__DIR__ . '/../templates');
        $twig = new \Twig\Environment($loader, [
            'cache' => false,
            'debug' => true,
        ]);
        
        return $twig;
    }
    
    private function isHttps() {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') 
            || $_SERVER['SERVER_PORT'] == 443;
    }
    
    private function getClientIp() {
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP']))
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_X_FORWARDED']))
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_FORWARDED']))
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        else if(isset($_SERVER['REMOTE_ADDR']))
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        else
            $ipaddress = 'UNKNOWN';
        return $ipaddress;
    }
    
    private function handleError(\Exception $e) {
        $template = $this->getTemplateEngine();
        
        return $template->render('error.php', [
            'error_code' => 500,
            'error_title' => 'Error',
            'error_message' => $e->getMessage(),
            'error_details' => $this->config['error_handling']['show_errors'] ? $e->getTraceAsString() : '',
            'show_debug' => $this->config['error_handling']['show_errors'],
            'debug_info' => json_encode([
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ], JSON_PRETTY_PRINT),
            'current_language' => $this->getCurrentLanguage(),
            'languages' => $this->config['languages'],
            'lang' => $this->getCurrentLanguageCode(),
            'site_title' => $this->config['server']['name'],
            'auto_redirect' => '/',
            // Translations
            ...$this->translations
        ]);
    }
}